import React, { useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { SimpleGame } from './SimpleGame';
import { ReactGameConfig } from '../types/config';
import { useGame } from '@repo/shared-game-utils/hooks/useGame';
import { useGameAtom } from '@repo/shared/lib/atoms/atom-extensions';
import { GameEventHandlers } from '../types/gameEvents';

interface PhaserGameComponentProps {
  eventHandlers?: GameEventHandlers;
}

interface PhaserGameComponentRef {
  restartGame: () => void;
}

export const PhaserGameComponent = forwardRef<PhaserGameComponentRef, PhaserGameComponentProps>(({ eventHandlers }, ref) => {
  const gameRef = useRef<HTMLDivElement>(null);
  const phaserGameRef = useRef<SimpleGame | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);


  const {config, resolveAssetUrl, widgetId} = useGame<ReactGameConfig>()
  const [soundEnabled, setSoundEnabled] = useGameAtom(widgetId, 'soundEnabled', true)

  const memoizedConfig = useMemo(() => config, [])

  useImperativeHandle(ref, () => ({
    restartGame: () => {
      if (phaserGameRef.current) {
        phaserGameRef.current.restartGame();
      }
    }
  }), []);


  useEffect(() => {
    if(!phaserGameRef.current) {
      return
    }
    phaserGameRef.current.game.sound.setMute(!soundEnabled)
    
  }, [soundEnabled, phaserGameRef.current])

  useEffect(() => {
    if (!gameRef.current) return;

    phaserGameRef.current = new SimpleGame(
      gameRef.current,
      memoizedConfig,
      resolveAssetUrl,
      eventHandlers
    );

    setIsLoaded(true);

    return () => {
      if (phaserGameRef.current) {
        phaserGameRef.current.destroy();
        phaserGameRef.current = null;
      }
      setIsLoaded(false);
    };
  }, [memoizedConfig]);

  return (
    <div className="absolute w-full h-full flex items-center justify-center" >
       {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-100">
          <div className="text-xl text-blue-800">Loading Phaser Game...</div>
        </div>
      )}
      <div 
        ref={gameRef} 
        className="w-full h-full"
      /> 
    </div>
  );
});