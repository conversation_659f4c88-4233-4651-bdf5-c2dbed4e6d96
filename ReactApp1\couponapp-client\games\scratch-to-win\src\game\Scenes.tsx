import React, { useEffect, useRef } from 'react'

import { AssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { useAtom } from 'jotai'
import {  gamePreviewScreenAtomFamily, selectedEditorItemAtom } from '@repo/shared/lib/atoms/editor-atoms'
import { defaultGameConfig, ReactGameConfig } from '../types/config'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'
import { useIsFirstRender } from '@repo/shared-game-utils/hooks/useIsFirstRender'
import { GameButton } from '@repo/shared-game-utils/components/GameButton'
import { GameContainer } from '@repo/shared-game-utils/components/GameContainer'
import { GameText } from '@repo/shared-game-utils/components/GameText'
import { useMusic } from '@repo/shared-game-utils/hooks/useSounds'
import { useGameRewards } from '@repo/shared/lib/game/useGameRewards'
import { RewardComponent } from '@repo/shared-game-utils/components/RewardComponent'
import { useGameState } from './GameMain'
import MainGame from './GameMain'


// Lose Life Screen - Shows the message when player loses a life
export const TryAgainScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
     useMusic(config.gameOverSound, true, false)

    

    return (
        <div className="w-full flex flex-col items-center">
            <GameButton
                config={config.tryAgainButton}
                dataConfigKey="tryAgainButton"
                onClick={(e) => {
                    e.stopPropagation()
                    if (onButtonClick) {
                        onButtonClick()
                    }
                }}
            />
        </div>
    )
}

// Game Over Screen - Shows when player reached the target score
export const GameOverScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)


   

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer config={config.gameOverOverlay} dataConfigKey="gameOverOverlay" resolveAssetUrl={resolveAssetUrl} style={{ padding: '2rem' }}>
                <GameText
                    config={config.gameOverTitle}
                    dataConfigKey="gameOverTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameText
                    config={config.gameOverMessage}
                    dataConfigKey="gameOverMessage"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />
                <GameButton
                    config={config.gameOverContinueButton || config.tryAgainButton}
                    dataConfigKey="gameOverContinueButton"
                    onClick={(e) => {
                        e.stopPropagation()
                        if (onButtonClick) {
                            onButtonClick()
                        }
                    }}
                />
            </GameContainer>
        </div>
    )
}

// Out of Lives Screen - Shows when player has no lives left
export const OutOfLivesScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, resolveAssetUrl } = useGame<ReactGameConfig>()
    useMusic(config.gameOverSound, true, false)

  
    return (
        <div className="h-full w-full flex flex-col items-center justify-center rounded-xl">
            <GameContainer config={config.outOfLivesOverlay} dataConfigKey="outOfLivesOverlay" resolveAssetUrl={resolveAssetUrl} style={{ padding: '2rem' }}>
                {config.outOfLivesTitle?.style?.isVisible !== false && (
                    <GameText
                        config={config.outOfLivesTitle}
                        dataConfigKey="outOfLivesTitle"
                        className="mb-4"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />
                )}

                {config.outOfLivesDescription?.style?.isVisible !== false && (
                    <GameText
                        config={config.outOfLivesDescription}
                        dataConfigKey="outOfLivesDescription"
                        className="mb-6"
                        onClick={(e) => {
                            e.stopPropagation()
                        }}
                    />
                )}

                {config.outOfLivesContinueButton?.isVisible !== false && (
                    <GameButton
                        config={config.outOfLivesContinueButton}
                        dataConfigKey="outOfLivesContinueButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                )}
            </GameContainer>
        </div>
    )
}

// Claim Reward Screen - Shows when player has won a reward
export const RewardScreen: React.FC<{ onButtonClick: () => void }> = ({ onButtonClick }) => {
    const { config, widgetId, resolveAssetUrl } = useGame<ReactGameConfig>()
    const { attemptsTaken } = useGameState()
    const { reward } = useGameRewards({ gameWidgetId: widgetId, attempt: attemptsTaken })
    useMusic(config.winSound, true, false)

    return (
        <div className="h-full w-full flex flex-col items-center justify-center">
            <GameContainer
                config={config.rewardOverlay}
                dataConfigKey="rewardOverlay"
                resolveAssetUrl={resolveAssetUrl}
                onClick={(e) => {
                    e.stopPropagation()
                }}
            >
                <GameText
                    config={config.rewardTitle}
                    dataConfigKey="rewardTitle"
                    className="mb-4"
                    onClick={(e) => {
                        e.stopPropagation()
                    }}
                />

                <div className="mb-4">
                    <RewardComponent dataConfigKey="rewardComponent" reward={reward} />
                </div>

                {config.gameRewardsHandler?.enableCtaButton !== false && (
                    <GameButton
                        config={config.rewardClaimButton || config.continueButton}
                        dataConfigKey="rewardClaimButton"
                        onClick={(e) => {
                            e.stopPropagation()
                            if (onButtonClick) {
                                onButtonClick()
                            }
                        }}
                    />
                )}
            </GameContainer>
        </div>
    )
}

export interface PreviewSceneProps {
    config: ReactGameConfig
    widgetId: string
    resolveAssetUrl: (id: AssetUrl) => string
}

export const PreviewScene: React.FC<PreviewSceneProps> = ({ config, widgetId, resolveAssetUrl }) => {
    const [selectedScreen] = useAtom(gamePreviewScreenAtomFamily(widgetId))
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [, _] = useAtom(selectedEditorItemAtom)
    const initialGameScreenChecked = useRef(false)

    const currentScreen = selectedScreen

    return (
        <MainGame config={config} widgetId={widgetId} isPreview={true} resolveAssetUrl={resolveAssetUrl} currentScreenId={currentScreen as any} initialGameScreenChecked={initialGameScreenChecked} 
        setCurrentScreenId={() => {}} defaultConfig={defaultGameConfig} />
    )
}
