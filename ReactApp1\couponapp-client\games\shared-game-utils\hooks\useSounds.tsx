import { AssetUrl, SoundAssetUrl } from '@repo/shared/lib/types/widgetSettings'
import { Howl } from 'howler'
import { atom, useAtom, useAtomValue, useSetAtom } from 'jotai'
import { useEffect, useMemo, useRef } from 'react'
import { atomWithDebounce, useGameAtom } from '@repo/shared/lib/atoms/atom-extensions'
import { useVisibilityChange } from '@uidotdev/usehooks'
import { useGame } from '@repo/shared-game-utils/hooks/useGame'

export const backgroundMusicAtom = atom<Howl>()
export const backgroundPlaybackRequestsAtom = atom<number>(0)
export const lastMusicAtom = atom<Howl>()
export const lastMusicPlayTimeAtom = atom(0)



export function useMusic(asset: SoundAssetUrl, autoplay: boolean = false, isMainBackgorundMusic: boolean = false) {
  const volume = asset.volume ?? 1
  const { resolveAssetUrl, widgetId, isPreview } = useGame()

  if (isPreview) {
    return
  }

  const [backgroundMusic, setBackgroundMusic] = useAtom(backgroundMusicAtom)
  const [lastMusic, setLastMusic] = useAtom(lastMusicAtom)
  const [lastMusicPlayTime, setLastMusicPlayTime] = useAtom(lastMusicPlayTimeAtom)

  const assetUrl = resolveAssetUrl(asset)

  const [soundEnabled] = useGameAtom(widgetId, 'soundEnabled', true)
  const documentVisible = useVisibilityChange()

  var sound = useMemo(() => {
    if(!asset?.enabled) {
      return null
    }

    if(!assetUrl) {
      return null
    }

    console.log("Shoud asset url: "+ assetUrl)
    return new Howl({
      src: assetUrl,
      html5: true,
      loop: isMainBackgorundMusic,

      onplay: function () {
      },

      onfade: function () {
      },

      onend: function () {
      },

      onstop: function () {
      }

    })

  }, [assetUrl, asset?.enabled])


  function play() {
     if(!asset?.enabled) {
      return
    }

    if(sound == null) return

    if (sound.playing()) return

    if (lastMusic != null && lastMusic.playing()) {
      lastMusic.fade(lastMusic.volume(), 0, 200)
    }

    if (backgroundMusic && backgroundMusic.playing() && backgroundMusic.volume() >= 0.01) {
      backgroundMusic.fade(backgroundMusic.volume(), 0, 200)
    }

       try {
          sound.fade(0, volume, 200)
        sound.play();
        setLastMusic(sound)
        setLastMusicPlayTime(Date.now())
    } catch  (e) {
      console.error("Error playing sound: ", e)
    }

  }

  useEffect(() => {

       if(!asset?.enabled) {
      return
    }

    if(!sound ) return

    if (isMainBackgorundMusic) {
      setBackgroundMusic(sound)
      console.log("Background music init there", assetUrl)
      return
    }

    if (autoplay) {
      play()
    }

    sound.once('unlock', function () {
      if (lastMusic != null) {
        return
      }

      if (autoplay) {
        play()
      }
    });

    return () => {
      if(!sound) return

      sound.fade(sound.volume(), 0, 200)
    }
  }, [sound, asset?.enabled])

  useEffect(() => {
    if (!isMainBackgorundMusic) {
      return
    }

    return () => {
      if(!sound) return
      sound.fade(sound.volume(), 0, 200)
      setTimeout(() => {
        sound.stop() 
      }, 300)
    }

  }, [sound, isMainBackgorundMusic])

  useEffect(() => {
    if (!soundEnabled || !documentVisible) {
      Howler.mute(true)
    } else {
      Howler.mute(false)
    }
  }, [soundEnabled, documentVisible])



  useEffect(() => {

    if(!asset?.enabled) {
      return
    }

    if (!isMainBackgorundMusic) {
      return
    }


    if (!backgroundMusic) {
      return
    }

    const interval = setInterval(() => {

      if (Date.now() - lastMusicPlayTime < 2000) {
        console.log("Diff between last music: ", Date.now() - lastMusicPlayTime)
        return
      }

      if (!backgroundMusic) {
        console.log("backgroundMusic is null")
        return
      }

      if (lastMusic?.playing() && lastMusic?.volume() > 0.01) {
        console.log("Last music is playing")
        return
      }

      if (backgroundMusic.playing() && backgroundMusic.volume() <= 0.01) {
        console.log("Just fade in back")
        backgroundMusic.fade(0, volume, 200)
        return
      }

      if (backgroundMusic.playing()) {
        console.log("backgroundMusic is playing")
        return
      }

      // console.log("Play start")
      backgroundMusic.fade(0, volume, 200)
      backgroundMusic.play()

    }, 600)

    return () => {
      clearInterval(interval)
    }

  }, [backgroundMusic, lastMusicPlayTime])

  return {}
}

export function useSoundEffect(asset: SoundAssetUrl) {
  const { resolveAssetUrl } = useGame()
  const volume =  asset.volume ?? 1

  const sound = useMemo(() => {
    if(!asset.enabled) {
      return null
    }

    return new Howl({
      src: resolveAssetUrl(asset),
      html5: true,
      loop: false,
      volume: volume
    })
  }, [resolveAssetUrl, asset])

  function play() {
    
    if(!asset?.enabled) {
      return
    }

    if(sound == null) return

    try {
      sound.play()
    } catch  (e) {
      console.error("Error playing sound: ", e)
    }
  }

  return { play }
}